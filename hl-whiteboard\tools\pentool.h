#ifndef PENTOOL_H
#define PENTOOL_H

#include "base/basetool.h"
#include <QMap>
#include <QVector>
#include <QPointF>

// 前向声明
class PenPathItem;

/**
 * @brief 画笔工具类
 *
 * 实现自由绘制功能，支持多指绘制。
 */
class PenTool : public BaseTool
{
    Q_OBJECT

public:
    explicit PenTool(QObject *parent = nullptr);
    ~PenTool();

    // 重写基类接口
    void onInputPress(int inputId, const QPointF &scenePos) override;
    void onInputMove(int inputId, const QPointF &scenePos) override;
    void onInputRelease(int inputId, const QPointF &scenePos) override;

    // 工具操作控制
    void finishCurrentOperation() override;
    void cancelCurrentOperation() override;

private:
    // 路径创建和管理
    PenPathItem* createNewPath(const QPointF &startPoint);
    void finishPath(PenPathItem *pathItem);

private:
    // 绘制状态 - 支持多个同时进行的路径（多指绘制）
    QMap<int, PenPathItem*> m_activePaths;  // 每个输入ID对应的路径项
};

#endif // PENTOOL_H
