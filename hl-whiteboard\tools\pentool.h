#ifndef PENTOOL_H
#define PENTOOL_H

#include "base/basetool.h"
#include <QMap>
#include <QVector>
#include <QPointF>

// 前向声明
class PenPathItem;

/**
 * @brief 路径段信息结构
 *
 * 用于管理分段绘制中的每个路径段
 */
struct PathSegment {
    PenPathItem* pathItem;      // 路径项指针
    int pointCount;             // 当前段点数
    bool isActive;              // 是否为活跃段（正在绘制）

    PathSegment() : pathItem(nullptr), pointCount(0), isActive(false) {}
    PathSegment(PenPathItem* item) : pathItem(item), pointCount(0), isActive(true) {}
};

/**
 * @brief 画笔工具类
 *
 * 实现自由绘制功能，支持多指绘制。
 */
class PenTool : public BaseTool
{
    Q_OBJECT

public:
    explicit PenTool(QObject *parent = nullptr);
    ~PenTool();

    // 重写基类接口
    void onInputPress(int inputId, const QPointF &scenePos) override;
    void onInputMove(int inputId, const QPointF &scenePos) override;
    void onInputRelease(int inputId, const QPointF &scenePos) override;

    // 工具操作控制
    void finishCurrentOperation() override;
    void cancelCurrentOperation() override;

private:
    // 路径创建和管理
    PenPathItem* createNewPath(const QPointF &startPoint);
    void finishPath(PenPathItem *pathItem);

    // 分段绘制相关方法
    bool shouldCreateNewSegment(int inputId) const;
    void createNewSegment(int inputId, const QPointF &point);
    void finishCurrentSegment(int inputId);
    void finishAllSegments(int inputId);
    QVector<QPointF> getOverlapPoints(const PathSegment &segment) const;

private:
    // 分段绘制参数
    static constexpr int MAX_POINTS_PER_SEGMENT = 100;  // 每段最大点数
    static constexpr int OVERLAP_POINTS = 5;            // 段间重叠点数

    // 绘制状态 - 支持多个同时进行的路径（多指绘制）
    QMap<int, QVector<PathSegment>> m_activePathSegments;  // 每个输入ID对应的路径段列表
};

#endif // PENTOOL_H
