#ifndef PENTOOL_H
#define PENTOOL_H

#include "base/basetool.h"
#include <QMap>
#include <QVector>
#include <QPointF>
#include <QGraphicsItemGroup>
#include <QElapsedTimer>

// 前向声明
class PenPathItem;

/**
 * @brief 临时路径段结构
 *
 * 用于管理分段绘制中的每个临时路径段
 */
struct TempPathSegment {
    PenPathItem* pathItem;           // 临时路径项
    int pointCount;                  // 当前段点数
    bool isActive;                   // 是否为活跃段（正在绘制）
    int segmentIndex;                // 段索引
    QVector<QPointF> segmentPoints;  // 段内所有点（用于最终合并）

    TempPathSegment()
        : pathItem(nullptr), pointCount(0), isActive(false), segmentIndex(-1) {}

    TempPathSegment(PenPathItem* item, int index)
        : pathItem(item), pointCount(0), isActive(true), segmentIndex(index) {}
};

/**
 * @brief 输入路径组管理结构
 *
 * 管理单个输入ID对应的所有临时段和最终路径
 */
struct InputPathGroup {
    QGraphicsItemGroup* tempGroup;              // 临时段组
    QVector<TempPathSegment> segments;          // 段列表
    QVector<QPointF> allCollectedPoints;        // 收集的所有点
    PenPathItem* finalPath;                     // 最终路径（绘制完成后）
    bool isDrawingComplete;                     // 绘制是否完成

    InputPathGroup()
        : tempGroup(nullptr), finalPath(nullptr), isDrawingComplete(false) {}
};

/**
 * @brief 画笔工具类
 *
 * 实现自由绘制功能，支持多指绘制。
 */
class PenTool : public BaseTool
{
    Q_OBJECT

public:
    explicit PenTool(QObject *parent = nullptr);
    ~PenTool();

    // 重写基类接口
    void onInputPress(int inputId, const QPointF &scenePos) override;
    void onInputMove(int inputId, const QPointF &scenePos) override;
    void onInputRelease(int inputId, const QPointF &scenePos) override;

    // 工具操作控制
    void finishCurrentOperation() override;
    void cancelCurrentOperation() override;

private:
    // 路径创建和管理
    PenPathItem* createNewPath(const QPointF &startPoint);
    void finishPath(PenPathItem *pathItem);

    // 分段绘制核心方法
    void initializeInputGroup(int inputId, const QPointF &startPoint);
    void createNewSegment(int inputId, const QPointF &point);
    void finishCurrentSegment(int inputId);
    QVector<QPointF> getOverlapPoints(const TempPathSegment &segment) const;

    // 最终路径合并
    void createFinalPath(int inputId);
    void cleanupTempSegments(int inputId);
    PenPathItem* mergeTempSegments(const QVector<QPointF> &allPoints);

    // 组管理辅助方法
    QGraphicsItemGroup* createOptimizedGroup();
    void optimizeSegmentForCaching(TempPathSegment &segment);

private:
    // 分段绘制参数
    static constexpr int MAX_POINTS_PER_SEGMENT = 100;  // 每段最大点数
    static constexpr int OVERLAP_POINTS = 5;            // 段间重叠点数

    // 绘制状态 - 支持多个同时进行的路径（多指绘制）
    QMap<int, InputPathGroup> m_inputGroups;  // 每个输入ID对应的路径组

    // 性能监控
    QElapsedTimer m_lastMoveTimer;              // 上次onInputMove的时间
    bool m_firstMove;                           // 是否是第一次移动
};

#endif // PENTOOL_H
