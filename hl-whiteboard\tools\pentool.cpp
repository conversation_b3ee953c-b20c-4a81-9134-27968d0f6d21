#include "pentool.h"
#include "../items/penpathitem.h"
#include <QDebug>
#include <QElapsedTimer>

PenTool::PenTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Pen, parent)
{
}

PenTool::~PenTool()
{
    finishCurrentOperation();
}

void PenTool::onInputPress(int inputId, const QPointF &scenePos)
{
    m_isDrawing = true;

    // 清理该输入ID的旧路径组（如果存在）
    if (m_inputGroups.contains(inputId)) {
        cleanupTempSegments(inputId);
        m_inputGroups.remove(inputId);
    }

    // 初始化新的输入路径组
    initializeInputGroup(inputId, scenePos);
}

void PenTool::onInputMove(int inputId, const QPointF &scenePos)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];
    if (group.segments.isEmpty()) {
        return;
    }

    // 收集点到总点列表（用于最终合并）
    group.allCollectedPoints.append(scenePos);

    // 检查是否需要创建新段
    if (shouldCreateNewSegment(inputId)) {
        createNewSegment(inputId, scenePos);
    } else {
        // 添加点到当前活跃段
        TempPathSegment &activeSegment = group.segments.last();
        if (activeSegment.isActive && activeSegment.pathItem) {
            activeSegment.pathItem->addPoint(scenePos);
            activeSegment.pointCount++;
            activeSegment.segmentPoints.append(scenePos);
        }
    }
}

void PenTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    // 添加最后一个点
    group.allCollectedPoints.append(scenePos);

    // 完成当前活跃段
    if (!group.segments.isEmpty()) {
        TempPathSegment &activeSegment = group.segments.last();
        if (activeSegment.isActive && activeSegment.pathItem) {
            activeSegment.pathItem->addPoint(scenePos);
            activeSegment.pointCount++;
            activeSegment.segmentPoints.append(scenePos);
        }
    }

    // 创建最终路径并清理临时段
    createFinalPath(inputId);
    cleanupTempSegments(inputId);

    // 清理输入组
    m_inputGroups.remove(inputId);

    // 如果没有活跃路径了，设置为非绘制状态
    if (m_inputGroups.isEmpty()) {
        m_isDrawing = false;
    }
}

void PenTool::finishCurrentOperation()
{
    // 完成所有活跃的输入组
    for (auto it = m_inputGroups.begin(); it != m_inputGroups.end(); ++it) {
        int inputId = it.key();
        createFinalPath(inputId);
        cleanupTempSegments(inputId);
    }
    m_inputGroups.clear();
    m_isDrawing = false;

    BaseTool::finishCurrentOperation();
}

void PenTool::cancelCurrentOperation()
{
    // 取消所有活跃的输入组
    for (auto it = m_inputGroups.begin(); it != m_inputGroups.end(); ++it) {
        cleanupTempSegments(it.key());
    }
    m_inputGroups.clear();
    m_isDrawing = false;

    BaseTool::cancelCurrentOperation();
}

PenPathItem* PenTool::createNewPath(const QPointF &startPoint)
{
    PenPathItem *pathItem = new PenPathItem();
    pathItem->addPoint(startPoint);

    addItemToScene(pathItem);

    return pathItem;
}

void PenTool::finishPath(PenPathItem *pathItem)
{
    if (pathItem) {
        emit itemFinished(pathItem);
    }
}

// ==================== 分段绘制核心方法实现 ====================

void PenTool::initializeInputGroup(int inputId, const QPointF &startPoint)
{
    InputPathGroup group;

    // 创建优化的临时段组
    group.tempGroup = createOptimizedGroup();
    addItemToScene(group.tempGroup);

    // 收集起始点
    group.allCollectedPoints.append(startPoint);

    // 创建第一个临时段
    PenPathItem *firstSegment = new PenPathItem();
    firstSegment->addPoint(startPoint);

    // 应用样式设置
    if (auto pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(firstSegment)) {
        pathItem->setPen(getCurrentPen());
        pathItem->setBrush(getCurrentBrush());
    }

    // 只添加到组
    group.tempGroup->addToGroup(firstSegment);

    // 创建段结构
    TempPathSegment tempSegment(firstSegment, 0);
    tempSegment.pointCount = 1;
    tempSegment.segmentPoints.append(startPoint);
    group.segments.append(tempSegment);

    // 保存到输入组映射
    m_inputGroups[inputId] = group;
}

bool PenTool::shouldCreateNewSegment(int inputId) const
{
    if (!m_inputGroups.contains(inputId)) {
        return false;
    }

    const InputPathGroup &group = m_inputGroups[inputId];
    if (group.segments.isEmpty()) {
        return false;
    }

    const TempPathSegment &activeSegment = group.segments.last();
    return activeSegment.isActive && activeSegment.pointCount >= MAX_POINTS_PER_SEGMENT;
}

void PenTool::createNewSegment(int inputId, const QPointF &point)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    // 完成当前活跃段
    finishCurrentSegment(inputId);

    // 获取重叠点
    QVector<QPointF> overlapPoints;
    if (!group.segments.isEmpty()) {
        overlapPoints = getOverlapPoints(group.segments.last());
    }

    // 创建新的路径段
    PenPathItem *newSegment = new PenPathItem();

    // 添加重叠点
    for (const QPointF &overlapPoint : overlapPoints) {
        newSegment->addPoint(overlapPoint);
    }

    // 添加当前点
    newSegment->addPoint(point);

    // 应用样式设置
    if (auto pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(newSegment)) {
        pathItem->setPen(getCurrentPen());
        pathItem->setBrush(getCurrentBrush());
    }

    // 只添加到组（组会自动管理场景添加）
    group.tempGroup->addToGroup(newSegment);

    // 创建新的段结构
    TempPathSegment tempSegment(newSegment, group.segments.size());
    tempSegment.pointCount = overlapPoints.size() + 1;
    tempSegment.segmentPoints = overlapPoints;
    tempSegment.segmentPoints.append(point);

    group.segments.append(tempSegment);
}

void PenTool::finishCurrentSegment(int inputId)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];
    if (!group.segments.isEmpty()) {
        TempPathSegment &activeSegment = group.segments.last();
        if (activeSegment.isActive) {
            activeSegment.isActive = false;
            // 优化段以便缓存
            optimizeSegmentForCaching(activeSegment);
        }
    }
}

QVector<QPointF> PenTool::getOverlapPoints(const TempPathSegment &segment) const
{
    QVector<QPointF> overlapPoints;

    if (segment.segmentPoints.size() <= OVERLAP_POINTS) {
        // 如果段点数不足，返回所有点
        overlapPoints = segment.segmentPoints;
    } else {
        // 取最后OVERLAP_POINTS个点
        int startIndex = segment.segmentPoints.size() - OVERLAP_POINTS;
        for (int i = startIndex; i < segment.segmentPoints.size(); ++i) {
            overlapPoints.append(segment.segmentPoints[i]);
        }
    }

    return overlapPoints;
}

// ==================== 最终路径合并方法 ====================

void PenTool::createFinalPath(int inputId)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    // 如果没有收集到点，直接返回
    if (group.allCollectedPoints.isEmpty()) {
        return;
    }

    // 创建最终路径
    group.finalPath = mergeTempSegments(group.allCollectedPoints);

    if (group.finalPath) {
        // 添加到场景
        addItemToScene(group.finalPath);

        // 发射完成信号
        emit itemFinished(group.finalPath);

        group.isDrawingComplete = true;
    }
}

void PenTool::cleanupTempSegments(int inputId)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    // 从场景移除临时组（会自动移除所有子项）
    if (group.tempGroup) {
        removeItemFromScene(group.tempGroup);
        group.tempGroup = nullptr;
    }

    // 清理段列表
    group.segments.clear();
}

PenPathItem* PenTool::mergeTempSegments(const QVector<QPointF> &allPoints)
{
    if (allPoints.isEmpty()) {
        return nullptr;
    }

    // 创建新的路径项
    PenPathItem *finalPath = new PenPathItem();

    // 添加所有点到最终路径
    for (const QPointF &point : allPoints) {
        finalPath->addPoint(point);
    }

    return finalPath;
}

// ==================== 组管理辅助方法 ====================

QGraphicsItemGroup* PenTool::createOptimizedGroup()
{
    QGraphicsItemGroup *group = new QGraphicsItemGroup();

    // 设置性能优化标志
    group->setFlag(QGraphicsItem::ItemHasNoContents, true);
    group->setHandlesChildEvents(false);

    // 设置缓存模式以提高性能
    group->setCacheMode(QGraphicsItem::DeviceCoordinateCache);

    return group;
}

void PenTool::optimizeSegmentForCaching(TempPathSegment &segment)
{
    if (segment.pathItem) {
        // 为非活跃段设置缓存模式
        segment.pathItem->setCacheMode(QGraphicsItem::ItemCoordinateCache);
    }
}


