#include "pentool.h"
#include "../items/penpathitem.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include <QDebug>

PenTool::PenTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Pen, parent)
    , m_firstMove(true)
{
    // 启用性能监控
    DRAWING_PROFILER_ENABLE(true);
    qDebug() << "[PenTool] 构造完成，性能监控已启用";
}

PenTool::~PenTool()
{
    qDebug() << "[PenTool] 析构开始";

    // 打印性能统计
    DRAWING_PROFILER_SUMMARY();

    finishCurrentOperation();

    qDebug() << "[PenTool] 析构完成";
}

void PenTool::onInputPress(int inputId, const QPointF &scenePos)
{
    DRAWING_TIMER("PenTool::onInputPress");

    m_isDrawing = true;

    qDebug() << "[PenTool] onInputPress - inputId:" << inputId << "pos:" << scenePos;

    // 清理该输入ID的旧路径组（如果存在）
    if (m_inputGroups.contains(inputId)) {
        qDebug() << "[PenTool] 清理旧路径组 - inputId:" << inputId;
        cleanupTempSegments(inputId);
        m_inputGroups.remove(inputId);
    }

    // 初始化新的输入路径组
    initializeInputGroup(inputId, scenePos);

    // 重置移动计时器
    m_firstMove = true;

    qDebug() << "[PenTool] 输入组总数:" << m_inputGroups.size();
}

void PenTool::onInputMove(int inputId, const QPointF &scenePos)
{
    // 计算与上次调用的时间间隔
    qint64 intervalMs = 0;
    if (m_firstMove) {
        m_lastMoveTimer.start();
        m_firstMove = false;
        qDebug() << "[PenTool] 首次onInputMove";
    } else {
        intervalMs = m_lastMoveTimer.restart();
        // 只在间隔超过5ms时打印（避免过多日志）
        if (intervalMs >= 5) {
            qDebug() << "[PenTool] onInputMove间隔:" << intervalMs << "ms";
        }
    }

    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];
    if (group.segments.isEmpty()) {
        return;
    }

    // 收集点到总点列表（用于最终合并）
    group.allCollectedPoints.append(scenePos);

    qDebug() << "[PenTool] onInputMove处理点 - inputId:" << inputId
             << "总段数:" << group.segments.size()
             << "总收集点数:" << group.allCollectedPoints.size();

    // 检查是否需要创建新段（在添加点之前检查）
    if (!group.segments.isEmpty()) {
        TempPathSegment &activeSegment = group.segments.last();
        qDebug() << "[PenTool] 当前活跃段状态 - 段索引:" << activeSegment.segmentIndex
                 << "点数:" << activeSegment.pointCount
                 << "是否活跃:" << activeSegment.isActive
                 << "是否需要分段:" << (activeSegment.pointCount >= MAX_POINTS_PER_SEGMENT);

        if (activeSegment.isActive && activeSegment.pointCount >= MAX_POINTS_PER_SEGMENT) {
            qDebug() << "[PenTool] 触发分段条件 - inputId:" << inputId << "当前段数:" << group.segments.size()
                     << "当前段点数:" << activeSegment.pointCount;
            createNewSegment(inputId, scenePos);
            qDebug() << "[PenTool] 分段完成，新段数:" << group.segments.size();
            return; // 新段创建时已经添加了当前点，直接返回
        }
    } else {
        qDebug() << "[PenTool] 警告：段列表为空！";
    }

    // 添加点到当前活跃段
    if (!group.segments.isEmpty()) {
        TempPathSegment &activeSegment = group.segments.last();
        qDebug() << "[PenTool] 添加点到段 - 段索引:" << activeSegment.segmentIndex
                 << "当前点数:" << activeSegment.pointCount;

        if (activeSegment.isActive && activeSegment.pathItem) {
            // 监控addPoint的耗时
            int timerId = DRAWING_TIMER_START("PenPathItem::addPoint");
            activeSegment.pathItem->addPoint(scenePos);
            DRAWING_TIMER_END(timerId);

            activeSegment.pointCount++;
            activeSegment.segmentPoints.append(scenePos);

            qDebug() << "[PenTool] 点添加完成 - 段索引:" << activeSegment.segmentIndex
                     << "新点数:" << activeSegment.pointCount;

            // 每100个点打印一次状态
            if (activeSegment.pointCount % 100 == 0) {
                qDebug() << "[PenTool] 段点数达到:" << activeSegment.pointCount << "总收集点数:" << group.allCollectedPoints.size();
            }
        } else {
            qDebug() << "[PenTool] 错误：活跃段无效 - isActive:" << activeSegment.isActive
                     << "pathItem:" << (activeSegment.pathItem != nullptr);
        }
    } else {
        qDebug() << "[PenTool] 错误：添加点时段列表为空！";
    }
}

void PenTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    // 添加最后一个点
    group.allCollectedPoints.append(scenePos);

    // 完成当前活跃段
    if (!group.segments.isEmpty()) {
        TempPathSegment &activeSegment = group.segments.last();
        if (activeSegment.isActive && activeSegment.pathItem) {
            activeSegment.pathItem->addPoint(scenePos);
            activeSegment.pointCount++;
            activeSegment.segmentPoints.append(scenePos);
        }
    }

    // 创建最终路径并清理临时段
    createFinalPath(inputId);
    cleanupTempSegments(inputId);

    // 清理输入组
    m_inputGroups.remove(inputId);

    // 如果没有活跃路径了，设置为非绘制状态
    if (m_inputGroups.isEmpty()) {
        m_isDrawing = false;
    }
}

void PenTool::finishCurrentOperation()
{
    // 完成所有活跃的输入组
    for (auto it = m_inputGroups.begin(); it != m_inputGroups.end(); ++it) {
        int inputId = it.key();
        createFinalPath(inputId);
        cleanupTempSegments(inputId);
    }
    m_inputGroups.clear();
    m_isDrawing = false;

    BaseTool::finishCurrentOperation();
}

void PenTool::cancelCurrentOperation()
{
    // 取消所有活跃的输入组
    for (auto it = m_inputGroups.begin(); it != m_inputGroups.end(); ++it) {
        cleanupTempSegments(it.key());
    }
    m_inputGroups.clear();
    m_isDrawing = false;

    BaseTool::cancelCurrentOperation();
}

PenPathItem* PenTool::createNewPath(const QPointF &startPoint)
{
    PenPathItem *pathItem = new PenPathItem();
    pathItem->addPoint(startPoint);

    addItemToScene(pathItem);

    return pathItem;
}

void PenTool::finishPath(PenPathItem *pathItem)
{
    if (pathItem) {
        emit itemFinished(pathItem);
    }
}

// ==================== 分段绘制核心方法实现 ====================

void PenTool::initializeInputGroup(int inputId, const QPointF &startPoint)
{
    InputPathGroup group;

    // 创建优化的临时段组
    group.tempGroup = createOptimizedGroup();
    addItemToScene(group.tempGroup);

    // 收集起始点
    group.allCollectedPoints.append(startPoint);

    // 创建第一个临时段
    PenPathItem *firstSegment = new PenPathItem();
    QVector<QPointF> initialPoints;
    initialPoints.append(startPoint);
    firstSegment->setPoints(initialPoints);

    // 应用样式设置
    if (auto pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(firstSegment)) {
        pathItem->setPen(getCurrentPen());
        pathItem->setBrush(getCurrentBrush());
    }

    // 只添加到组
    group.tempGroup->addToGroup(firstSegment);

    // 创建段结构
    TempPathSegment tempSegment(firstSegment, 0);
    tempSegment.pointCount = 1;
    tempSegment.segmentPoints.append(startPoint);
    group.segments.append(tempSegment);

    // 保存到输入组映射
    m_inputGroups[inputId] = group;
}



void PenTool::createNewSegment(int inputId, const QPointF &point)
{
    DRAWING_TIMER("PenTool::createNewSegment");

    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    qDebug() << "[PenTool] 开始创建新段 - inputId:" << inputId << "当前段数:" << group.segments.size();

    qDebug() << "[PenTool] 开始完成当前活跃段";
    // 完成当前活跃段
    finishCurrentSegment(inputId);

    // 获取重叠点
    QVector<QPointF> overlapPoints;
    if (!group.segments.isEmpty()) {
        overlapPoints = getOverlapPoints(group.segments.last());
        qDebug() << "[PenTool] 获取重叠点数:" << overlapPoints.size();
    } else {
        qDebug() << "[PenTool] 警告：获取重叠点时段列表为空";
    }

    qDebug() << "[PenTool] 创建新的PenPathItem";
    // 创建新的路径段
    PenPathItem *newSegment = new PenPathItem();

    // 批量设置所有点（重叠点 + 当前点）
    QVector<QPointF> allPoints = overlapPoints;
    allPoints.append(point);
    qDebug() << "[PenTool] 批量设置" << allPoints.size() << "个点到新段";
    newSegment->setPoints(allPoints);

    // 应用样式设置
    if (auto pathItem = qgraphicsitem_cast<QGraphicsPathItem*>(newSegment)) {
        pathItem->setPen(getCurrentPen());
        pathItem->setBrush(getCurrentBrush());
        qDebug() << "[PenTool] 样式设置完成";
    } else {
        qDebug() << "[PenTool] 警告：样式设置失败";
    }

    // 只添加到组（组会自动管理场景添加）
    qDebug() << "[PenTool] 添加新段到组";
    group.tempGroup->addToGroup(newSegment);

    // 创建新的段结构
    int newSegmentIndex = group.segments.size();
    qDebug() << "[PenTool] 创建新段结构 - 索引:" << newSegmentIndex;
    TempPathSegment tempSegment(newSegment, newSegmentIndex);
    tempSegment.pointCount = overlapPoints.size() + 1;
    tempSegment.segmentPoints = overlapPoints;
    tempSegment.segmentPoints.append(point);

    qDebug() << "[PenTool] 添加新段到段列表 - 当前段数:" << group.segments.size();
    group.segments.append(tempSegment);
    qDebug() << "[PenTool] 段列表更新完成 - 新段数:" << group.segments.size();

    qDebug() << "[PenTool] 新段创建完成 - 段索引:" << tempSegment.segmentIndex
             << "初始点数:" << tempSegment.pointCount
             << "是否活跃:" << tempSegment.isActive;
}

void PenTool::finishCurrentSegment(int inputId)
{
    qDebug() << "[PenTool] finishCurrentSegment - inputId:" << inputId;

    if (!m_inputGroups.contains(inputId)) {
        qDebug() << "[PenTool] 错误：输入组不存在 - inputId:" << inputId;
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];
    qDebug() << "[PenTool] 当前段数:" << group.segments.size();

    if (!group.segments.isEmpty()) {
        TempPathSegment &activeSegment = group.segments.last();
        qDebug() << "[PenTool] 完成段 - 段索引:" << activeSegment.segmentIndex
                 << "点数:" << activeSegment.pointCount
                 << "当前是否活跃:" << activeSegment.isActive;

        if (activeSegment.isActive) {
            activeSegment.isActive = false;
            qDebug() << "[PenTool] 段已标记为非活跃";
            // 优化段以便缓存
            optimizeSegmentForCaching(activeSegment);
            qDebug() << "[PenTool] 段缓存优化完成";
        } else {
            qDebug() << "[PenTool] 警告：段已经是非活跃状态";
        }
    } else {
        qDebug() << "[PenTool] 警告：完成段时段列表为空";
    }
}

QVector<QPointF> PenTool::getOverlapPoints(const TempPathSegment &segment) const
{
    QVector<QPointF> overlapPoints;

    if (segment.segmentPoints.size() <= OVERLAP_POINTS) {
        // 如果段点数不足，返回所有点
        overlapPoints = segment.segmentPoints;
    } else {
        // 取最后OVERLAP_POINTS个点
        int startIndex = segment.segmentPoints.size() - OVERLAP_POINTS;
        for (int i = startIndex; i < segment.segmentPoints.size(); ++i) {
            overlapPoints.append(segment.segmentPoints[i]);
        }
    }

    return overlapPoints;
}

// ==================== 最终路径合并方法 ====================

void PenTool::createFinalPath(int inputId)
{
    DRAWING_TIMER("PenTool::createFinalPath");

    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    qDebug() << "[PenTool] 开始创建最终路径 - inputId:" << inputId
             << "段数:" << group.segments.size()
             << "总点数:" << group.allCollectedPoints.size();

    // 如果没有收集到点，直接返回
    if (group.allCollectedPoints.isEmpty()) {
        qDebug() << "[PenTool] 警告：没有收集到点，跳过最终路径创建";
        return;
    }

    // 创建最终路径
    group.finalPath = mergeTempSegments(group.allCollectedPoints);

    if (group.finalPath) {
        // 添加到场景
        addItemToScene(group.finalPath);

        // 发射完成信号
        emit itemFinished(group.finalPath);

        group.isDrawingComplete = true;

        qDebug() << "[PenTool] 最终路径创建完成 - inputId:" << inputId;
    } else {
        qDebug() << "[PenTool] 错误：最终路径创建失败 - inputId:" << inputId;
    }
}

void PenTool::cleanupTempSegments(int inputId)
{
    if (!m_inputGroups.contains(inputId)) {
        return;
    }

    InputPathGroup &group = m_inputGroups[inputId];

    // 从场景移除临时组（会自动移除所有子项）
    if (group.tempGroup) {
        removeItemFromScene(group.tempGroup);
        group.tempGroup = nullptr;
    }

    // 清理段列表
    group.segments.clear();
}

PenPathItem* PenTool::mergeTempSegments(const QVector<QPointF> &allPoints)
{
    if (allPoints.isEmpty()) {
        return nullptr;
    }

    // 创建新的路径项
    PenPathItem *finalPath = new PenPathItem();

    // 批量设置所有点到最终路径
    finalPath->setPoints(allPoints);

    return finalPath;
}

// ==================== 组管理辅助方法 ====================

QGraphicsItemGroup* PenTool::createOptimizedGroup()
{
    QGraphicsItemGroup *group = new QGraphicsItemGroup();

    // 设置性能优化标志
    group->setFlag(QGraphicsItem::ItemHasNoContents, true);
    group->setHandlesChildEvents(false);

    // 设置缓存模式以提高性能
    group->setCacheMode(QGraphicsItem::DeviceCoordinateCache);

    return group;
}

void PenTool::optimizeSegmentForCaching(TempPathSegment &segment)
{
    if (segment.pathItem) {
        // 为非活跃段设置缓存模式
        segment.pathItem->setCacheMode(QGraphicsItem::ItemCoordinateCache);
    }
}


