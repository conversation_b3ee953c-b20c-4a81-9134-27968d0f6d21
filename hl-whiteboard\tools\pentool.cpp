#include "pentool.h"
#include "../items/penpathitem.h"
#include <QDebug>

PenTool::PenTool(QObject *parent)
    : BaseTool(WhiteboardTypes::DrawingType::Pen, parent)
{
}

PenTool::~PenTool()
{
    finishCurrentOperation();
}

void PenTool::onInputPress(int inputId, const QPointF &scenePos)
{
    m_isDrawing = true;

    // 创建新的路径项
    PenPathItem *pathItem = createNewPath(scenePos);

    // 保存到活跃路径映射
    m_activePaths[inputId] = pathItem;
}

void PenTool::onInputMove(int inputId, const QPointF &scenePos)
{
    if (!m_activePaths.contains(inputId)) {
        return;
    }

    PenPathItem *pathItem = m_activePaths[inputId];
    if (pathItem) {
        // 直接添加点到路径
        pathItem->addPoint(scenePos);
    }
}

void PenTool::onInputRelease(int inputId, const QPointF &scenePos)
{
    if (!m_activePaths.contains(inputId)) {
        return;
    }

    PenPathItem *pathItem = m_activePaths[inputId];
    if (pathItem) {
        // 添加最后一个点
        pathItem->addPoint(scenePos);

        // 完成路径
        finishPath(pathItem);
    }

    // 清理状态
    m_activePaths.remove(inputId);

    // 如果没有活跃路径了，设置为非绘制状态
    if (m_activePaths.isEmpty()) {
        m_isDrawing = false;
    }
}

void PenTool::finishCurrentOperation()
{
    // 完成所有活跃的路径
    for (auto it = m_activePaths.begin(); it != m_activePaths.end(); ++it) {
        finishPath(it.value());
    }
    m_activePaths.clear();
    m_isDrawing = false;

    BaseTool::finishCurrentOperation();
}

void PenTool::cancelCurrentOperation()
{
    // 取消所有活跃的路径
    for (auto it = m_activePaths.begin(); it != m_activePaths.end(); ++it) {
        removeItemFromScene(it.value());
    }
    m_activePaths.clear();
    m_isDrawing = false;

    BaseTool::cancelCurrentOperation();
}

PenPathItem* PenTool::createNewPath(const QPointF &startPoint)
{
    PenPathItem *pathItem = new PenPathItem();
    pathItem->addPoint(startPoint);

    addItemToScene(pathItem);

    return pathItem;
}

void PenTool::finishPath(PenPathItem *pathItem)
{
    if (pathItem) {
        emit itemFinished(pathItem);
    }
}


