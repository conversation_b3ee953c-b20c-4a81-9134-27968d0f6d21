#include "penpathitem.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QWidget>
#include <QtMath>
#include <QDebug>
#include <QJsonArray>

PenPathItem::PenPathItem(QGraphicsItem *parent)
    : QGraphicsPathItem(parent)
    , DrawingItemBase(WhiteboardTypes::DrawingType::Pen, this)
    , m_smoothness(0.3)
{
}

PenPathItem::~PenPathItem()
{
}

QJsonObject PenPathItem::serializeGeometry() const
{
    QJsonObject json;

    // 路径特有信息
    json["smoothness"] = m_smoothness;

    // 路径点数据
    QJsonArray pointsArray;
    for (const QPointF &point : m_points) {
        QJsonObject pointObj;
        pointObj["x"] = point.x();
        pointObj["y"] = point.y();
        pointsArray.append(pointObj);
    }
    json["points"] = pointsArray;

    return json;
}

bool PenPathItem::deserializeGeometry(const QJsonObject &json)
{
    try {
        // 读取平滑度
        m_smoothness = json["smoothness"].toDouble(0.3);

        // 读取路径点
        m_points.clear();
        QJsonArray pointsArray = json["points"].toArray();
        for (const QJsonValue &value : pointsArray) {
            QJsonObject pointObj = value.toObject();
            QPointF point(pointObj["x"].toDouble(), pointObj["y"].toDouble());
            m_points.append(point);
        }

        // 重建路径
        rebuildPath();

        return true;
    } catch (...) {
        qWarning() << "PenPathItem::deserializeGeometry: Exception occurred";
        return false;
    }
}

void PenPathItem::addPoint(const QPointF &point)
{
    // 检查最小距离（性能优化）
    if (!m_points.isEmpty()) {
        QPointF lastPoint = m_points.last();
        qreal distance = QLineF(lastPoint, point).length();
        if (distance < MIN_POINT_DISTANCE) {
            return;  // 跳过太近的点
        }
    }

    m_points.append(point);

    // 监控rebuildPath的性能
    int timerId = DRAWING_TIMER_START(QString("rebuildPath(点数:%1)").arg(m_points.size()));
    rebuildPath();
    DRAWING_TIMER_END(timerId);
}

void PenPathItem::smoothPath()
{
    if (m_points.size() < 3) {
        return;  // 需要至少3个点才能平滑
    }
    
    QVector<QPointF> smoothedPoints = smoothPoints(m_points, m_smoothness);
    m_points = smoothedPoints;
    rebuildPath();
}

void PenPathItem::clearPath()
{
    m_points.clear();
    setPath(QPainterPath());
}

QVector<QPointF> PenPathItem::points() const
{
    return m_points;
}

int PenPathItem::pointCount() const
{
    return m_points.size();
}

bool PenPathItem::isEmpty() const
{
    return m_points.isEmpty();
}

void PenPathItem::setPoints(const QVector<QPointF> &points)
{
    m_points = points;
    rebuildPath();
}

void PenPathItem::simplifyPath(qreal tolerance)
{
    if (m_points.size() < 3) {
        return;
    }
    
    // 使用 Douglas-Peucker 算法简化路径
    QVector<QPointF> simplified;
    simplified.append(m_points.first());
    
    // 简化实现：移除距离小于容差的连续点
    for (int i = 1; i < m_points.size() - 1; ++i) {
        QPointF current = m_points[i];
        QPointF last = simplified.last();
        
        if (QLineF(last, current).length() >= tolerance) {
            simplified.append(current);
        }
    }
    
    simplified.append(m_points.last());
    
    if (simplified.size() != m_points.size()) {
        m_points = simplified;
        rebuildPath();
    }
}

void PenPathItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    // 打印实例
    qDebug() << "[PenPathItem] 绘制实例 - " << this;
    // 设置抗锯齿
    setupAntiAliasing(painter);
    
    // 调用父类绘制
    QGraphicsPathItem::paint(painter, option, widget);
}

QPainterPath PenPathItem::shape() const
{
    return path();
}

void PenPathItem::rebuildPath()
{
    // 详细监控rebuildPath内部耗时
    DRAWING_TIMER("PenPathItem::rebuildPath_internal");

    QPainterPath newPath;

    if (m_points.isEmpty()) {
        setPath(newPath);
        return;
    }

    if (m_points.size() == 1) {
        // 单点：绘制一个小圆点
        QPointF point = m_points.first();
        qreal radius = pen().widthF() / 2.0;
        newPath.addEllipse(point, radius, radius);
    } else if (m_points.size() == 2) {
        // 两点：直线
        newPath.moveTo(m_points[0]);
        newPath.lineTo(m_points[1]);
    } else {
        // 多点：平滑曲线
        newPath.moveTo(m_points[0]);

        for (int i = 1; i < m_points.size() - 1; ++i) {
            QPointF p1 = m_points[i - 1];
            QPointF p2 = m_points[i];
            QPointF p3 = m_points[i + 1];

            QPointF cp1 = calculateControlPoint(p1, p2, p3, m_smoothness);
            QPointF cp2 = calculateControlPoint(p3, p2, p1, m_smoothness);

            newPath.cubicTo(cp1, cp2, p2);
        }

        // 连接到最后一个点
        newPath.lineTo(m_points.last());
    }

    setPath(newPath);
}

QVector<QPointF> PenPathItem::smoothPoints(const QVector<QPointF> &points, qreal smoothness) const
{
    if (points.size() < 3) {
        return points;
    }
    
    QVector<QPointF> smoothed;
    smoothed.reserve(points.size());
    
    smoothed.append(points.first());
    
    for (int i = 1; i < points.size() - 1; ++i) {
        QPointF prev = points[i - 1];
        QPointF current = points[i];
        QPointF next = points[i + 1];
        
        // 计算平滑后的点
        QPointF smoothedPoint = current + (prev + next - 2 * current) * smoothness;
        smoothed.append(smoothedPoint);
    }
    
    smoothed.append(points.last());
    
    return smoothed;
}

QPointF PenPathItem::calculateControlPoint(const QPointF &p1, const QPointF &p2, const QPointF &p3, qreal smoothness) const
{
    QPointF direction = p3 - p1;
    return p2 + direction * smoothness * 0.25;
}